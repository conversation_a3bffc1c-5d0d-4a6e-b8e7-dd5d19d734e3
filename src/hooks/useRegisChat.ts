import { useState, useCallback, useEffect, useRef } from "react";
import { useKeycloak } from "@react-keycloak/web";
import {
  useRegisChatApi,
  generateSecureSessionId,
} from "../services/upivotalAgenticService";
import { ROUTES } from "../constants/routes";

export type OnboardingStep =
  | "initial"
  | "firstName"
  | "lastName"
  | "email"
  | "verification"
  | "password"
  | "completed";

export interface RegisMessage {
  id: string;
  sender: "user" | "regis";
  content: string;
  timestamp: Date;
  senderName: string;
}

export interface OnboardingData {
  firstName?: string;
  lastName?: string;
  email?: string;
  emailVerified?: boolean;
  password?: string;
}

export interface RegisState {
  messages: RegisMessage[];
  isLoading: boolean;
  sessionId: string;
  currentStep: OnboardingStep;
  onboardingData: OnboardingData;
  isSignupFlow: boolean;
}

const initialState: RegisState = {
  messages: [],
  isLoading: false,
  sessionId: generateSecureSessionId(),
  currentStep: "initial",
  onboardingData: {},
  isSignupFlow: false,
};

export const useRegisChat = (flowType: "signup" | "login") => {
  const [state, setState] = useState<RegisState>({
    ...initialState,
    isSignupFlow: flowType === "signup",
  });
  const hasInitializedRef = useRef(false);
  const chatWithRegis = useRegisChatApi();
  const { keycloak } = useKeycloak();

  const addMessage = useCallback(
    (sender: "user" | "regis", content: string, senderName: string) => {
      const newMessage: RegisMessage = {
        id: Date.now().toString(),
        sender,
        content,
        timestamp: new Date(),
        senderName,
      };

      setState((prev) => ({
        ...prev,
        messages: [...prev.messages, newMessage],
      }));
    },
    []
  );

  const setLoading = useCallback((loading: boolean) => {
    setState((prev) => ({ ...prev, isLoading: loading }));
  }, []);

  const hashPasswordForDisplay = useCallback(
    (message: string, currentStep: OnboardingStep) => {
      // Only hash if we're in the password step and the message looks like a password
      if (currentStep === "password" && message.length >= 8) {
        return "•".repeat(message.length);
      }
      return message;
    },
    []
  );

  const updateOnboardingData = useCallback((data: Partial<OnboardingData>) => {
    setState((prev) => ({
      ...prev,
      onboardingData: { ...prev.onboardingData, ...data },
    }));
  }, []);

  const updateCurrentStep = useCallback((step: OnboardingStep) => {
    setState((prev) => ({ ...prev, currentStep: step }));
  }, []);

  const extractOnboardingDataFromMessage = useCallback(
    (response: string) => {
      // Extract requestField from JSON in the response (both old and new formats)
      let requestFieldMatch = null;

      // Try new format first (markdown code blocks)
      const markdownJsonMatch = response.match(/```json\s*(\{.*?\})\s*```/gs);
      if (markdownJsonMatch) {
        try {
          const jsonContent = markdownJsonMatch[0].replace(
            /```json\s*|\s*```/g,
            ""
          );
          const parsedJson = JSON.parse(jsonContent);
          if (parsedJson.requestField) {
            requestFieldMatch = [null, parsedJson.requestField];
          }
        } catch (error) {
          console.warn("Failed to parse JSON from markdown block:", error);
        }
      }

      // Fallback to old format (plain JSON)
      if (!requestFieldMatch) {
        requestFieldMatch = response.match(/\{"requestField":"([^"]+)"\}/);
      }

      if (requestFieldMatch) {
        const requestField = requestFieldMatch[1];

        // Map requestField to OnboardingStep (case-insensitive)
        const stepMapping: Record<string, OnboardingStep> = {
          firstname: "firstName",
          lastname: "lastName",
          email: "email",
          verification: "verification",
          verificationcode: "verification",
          password: "password",
        };

        const normalizedField = requestField.toLowerCase();
        if (stepMapping[normalizedField]) {
          updateCurrentStep(stepMapping[normalizedField]);
        }
      } else if (state.isSignupFlow && state.currentStep === "password") {
        // Check for signup completion indicators when no requestField is present
        const lowerResponse = response.toLowerCase();
        const successIndicators = [
          "your account has been successfully created",
          "successfully created",
          "created successfully",
          "successful",
          "welcome aboard",
          "welcome",
        ];

        const isSignupComplete = successIndicators.some((indicator) =>
          lowerResponse.includes(indicator)
        );

        if (isSignupComplete) {
          updateCurrentStep("completed");
          // Trigger Keycloak login after signup completion
          setTimeout(() => {
            try {
              keycloak?.login({
                redirectUri: window.location.origin + ROUTES.DASHBOARD_BASE,
              });
            } catch (error) {
              console.error("Error initiating Keycloak login:", error);
              // Fallback: redirect to login page
              window.location.href = ROUTES.LOGIN;
            }
          }, 2000); // Give user time to see completion message
        }
      }
    },
    [updateCurrentStep, state.isSignupFlow, state.currentStep]
  );

  const sendMessage = useCallback(
    async (userMessage: string) => {
      try {
        // Add user message immediately (hash password for display)
        const userDisplayMessage = hashPasswordForDisplay(
          userMessage,
          state.currentStep
        );
        addMessage("user", userDisplayMessage, "You");

        // Set loading state
        setLoading(true);

        // Send message to API
        const response = await chatWithRegis({
          userMessage,
          sessionId: state.sessionId,
        });

        // Extract onboarding data from Regis's response
        extractOnboardingDataFromMessage(response);

        // Add Regis response (display text only, without JSON metadata)
        let displayMessage = response;

        // Remove JSON markdown blocks from display (new format)
        displayMessage = displayMessage
          .replace(/```json\s*\{.*?\}\s*```/gs, "")
          .trim();

        // Remove plain JSON from display (old format - for backward compatibility)
        displayMessage = displayMessage
          .replace(/\{"requestField":"[^"]+"\}\s*/g, "")
          .trim();

        addMessage("regis", displayMessage, "Regis");
      } catch (error) {
        console.error("Chat error:", error);
        addMessage(
          "regis",
          "I apologize, but I encountered an error. Please try again.",
          "Regis"
        );
      } finally {
        setLoading(false);
      }
    },
    [
      addMessage,
      setLoading,
      chatWithRegis,
      state.sessionId,
      state.currentStep,
      extractOnboardingDataFromMessage,
      hashPasswordForDisplay,
    ]
  );

  // Auto-trigger initial message based on flow type
  useEffect(() => {
    if (!hasInitializedRef.current && state.messages.length === 0) {
      hasInitializedRef.current = true;
      if (flowType === "signup") {
        sendMessage("Hey Regis, I want to create a new account.");
      } else {
        // For login, just add the initial Regis message without sending to API
        addMessage(
          "regis",
          "Welcome back. You can go ahead and type your email and password into the fields on the right to log in. If you need help with your password or anything else, please let me know!",
          "Regis"
        );
      }
    }
  }, [flowType, state.messages.length, sendMessage, addMessage]);

  return {
    state,
    sendMessage,
    addMessage,
    setLoading,
    updateOnboardingData,
    updateCurrentStep,
  };
};
