import { cn } from '@/lib/twMerge/cn';
import * as AvatarPrimitive from '@radix-ui/react-avatar';
import * as React from 'react';
import { useMemo } from 'react';

function generateColor(seed: string): string {
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    hash = seed.charCodeAt(i) + ((hash << 5) - hash);
  }

  const h = hash % 360;
  const s = 50 + (hash % 30); // Saturation between 50% and 80%
  const l = 65 + (hash % 20); // Lightness between 65% and 85%

  return `hsl(${h}, ${s}%, ${l}%)`;
}

function getContrastColor(bgColor: string): string {
  const hsl = bgColor.match(/\d+/g)?.map(Number);
  if (!hsl || hsl.length !== 3) return '#000000';

  const l = hsl[2];
  return l > 60 ? '#000000' : '#FFFFFF';
}

const Avatar = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>
>(({ className, ...props }, ref) => (
  <AvatarPrimitive.Root
    ref={ref}
    className={cn(
      'relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full',
      className,
    )}
    {...props}
  />
));
Avatar.displayName = AvatarPrimitive.Root.displayName;

const AvatarImage = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Image>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>
>(({ className, ...props }, ref) => (
  <AvatarPrimitive.Image
    ref={ref}
    className={cn('aspect-square h-full w-full', className)}
    {...props}
  />
));
AvatarImage.displayName = AvatarPrimitive.Image.displayName;

interface AvatarFallbackProps extends React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback> {
  fullName?: string;
  maxNameLength?: number;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  useRandomColors?: boolean;
}

const AvatarFallback = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Fallback>,
  AvatarFallbackProps
>(({ className, fullName = '', maxNameLength = 20, size = 'md', useRandomColors = false, children, ...props }, ref) => {
  const truncatedName = fullName.length > maxNameLength
    ? `${fullName.slice(0, maxNameLength)}...`
    : fullName;

  const initials = fullName
    .split(' ')
    .map((name) => name.charAt(0).toUpperCase())
    .join('');

  const backgroundColor = useMemo(() => {
    return useRandomColors && fullName ? generateColor(fullName) : undefined;
  }, [fullName, useRandomColors]);
  
  const textColor = useMemo(() => {
    return backgroundColor ? getContrastColor(backgroundColor) : undefined;
  }, [backgroundColor]);

  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-11 h-11 text-sm', 
    lg: 'w-14 h-14 text-base',
    xl: 'w-20 h-20 text-lg',
  };

  const fallbackStyle = useRandomColors && backgroundColor && textColor ? {
    backgroundColor,
    color: textColor,
  } : undefined;

  return (
    <AvatarPrimitive.Fallback
      ref={ref}
      className={cn(
        'bg-muted flex h-full w-full items-center justify-center rounded-full',
        useRandomColors ? '' : 'bg-muted',
        className
      )}
      style={fallbackStyle}
      {...props}
    >
      {children || (useRandomColors && fullName ? initials : children)}
    </AvatarPrimitive.Fallback>
  );
});
AvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;

export { Avatar, AvatarFallback, AvatarImage };
