import React, { useEffect, useRef, useState, useCallback } from "react";
import { User, ArrowDown } from "lucide-react";
import moment from "moment";
import ReactMarkdown from "react-markdown";
import { scyra } from "../../assets/images";
import { TypingIndicator } from "./TypingIndicator";
import { useTenant } from "@/context/TenantContext";
import {
  EnhancedScyraChatInterfaceProps,
  ScyraMessage,
  FormField,
} from "../../types/businessStack";
import { useGetUserProfile } from "../../hooks/useUserProfile";
import { UserBasicInfoPayload } from "../../types/user";
import { getAgentAvatar } from "../../utils/agentUtils";

const ScyraMessageComponent = ({ message }: { message: ScyraMessage }) => {
  const isUser = message.sender === "user";
  const { activeAgent } = useTenant();
  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();

  const agentAvatar = getAgentAvatar(activeAgent, userData);

  return (
    <div className="flex gap-3 mb-6">
      {/* Avatar */}
      <div className="flex-shrink-0 w-10 h-10">
        {isUser ? (
          <div className="flex justify-center items-center w-full h-full rounded-full bg-grayTwentySix">
            <User className="w-5 h-5 text-gray-600" />
          </div>
        ) : (
          <div className="rounded-full bg-peachTwo">
            <img
              src={agentAvatar}
              alt={activeAgent ? `${activeAgent} Agent` : "Agent"}
              className="object-cover w-full h-full rounded-full"
            />
          </div>
        )}
      </div>

      {/* Message Content */}
      <div className="flex-1">
        {/* Header */}
        <div className="flex gap-2 items-center mb-1">
          <span className="font-semibold text-darkGray">
            {message.senderName}
          </span>
          <span className="text-sm text-gray-400">
            {moment(message.timestamp).format("h:mm A")}
          </span>
        </div>

        {/* Message Text */}
        <div className="p-3 rounded-lg bg-gray-5 text-grayTwentyFour">
          <ReactMarkdown
            components={{
              // Customize markdown components to match existing styling
              p: (props) => <p className="mb-2 last:mb-0">{props.children}</p>,
              strong: (props) => (
                <strong className="font-semibold text-darkGray">
                  {props.children}
                </strong>
              ),
              em: (props) => (
                <em className="italic text-gray-600">{props.children}</em>
              ),
              code: (props) => (
                <code className="rounded bg-gray-100 px-1 py-0.5 font-mono text-sm text-gray-800">
                  {props.children}
                </code>
              ),
              pre: (props) => (
                <pre className="overflow-x-auto p-2 mt-2 font-mono text-sm text-gray-800 bg-gray-100 rounded">
                  {props.children}
                </pre>
              ),
              ul: (props) => (
                <ul className="ml-4 space-y-1 list-disc">{props.children}</ul>
              ),
              ol: (props) => (
                <ol className="ml-4 space-y-1 list-decimal">
                  {props.children}
                </ol>
              ),
              li: (props) => (
                <li className="text-grayTwentyFour">{props.children}</li>
              ),
              a: (props) => (
                <a
                  href={props.href}
                  className="underline hover:text-primaryDark text-primary"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {props.children}
                </a>
              ),
            }}
          >
            {message.content}
          </ReactMarkdown>
        </div>
      </div>
    </div>
  );
};

export const EnhancedScyraChatInterface = ({
  state,
  ChatInputComponent,
  groupedMessages,
  connectionFlow,
  originalSendMessage,
}: EnhancedScyraChatInterfaceProps) => {
  const { activeAgent } = useTenant();
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [currentPlaceholder, setCurrentPlaceholder] = useState<string>("");
  const [isUserAtBottom, setIsUserAtBottom] = useState(true);

  const capitalizeAgentName = (key: string) => {
    if (!key) return "Agent";
    return key.charAt(0).toUpperCase() + key.slice(1);
  };

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  };

  // Get current field for connection flow
  const getCurrentField = useCallback((): FormField | null => {
    if (
      !connectionFlow?.state.currentApp ||
      connectionFlow.state.fieldsToCollect.length === 0
    ) {
      return null;
    }
    return (
      connectionFlow.state.fieldsToCollect[
        connectionFlow.state.currentFieldIndex
      ] || null
    );
  }, [connectionFlow]);

  // Update placeholder based on connection flow state
  useEffect(() => {
    if (!connectionFlow) return;

    const currentField = getCurrentField();
    if (currentField) {
      setCurrentPlaceholder(
        currentField.placeholder || `Enter your ${currentField.label}`
      );
    } else {
      setCurrentPlaceholder("");
    }
  }, [connectionFlow, getCurrentField]);

  // Handle user input during connection flows
  const handleConnectionInput = useCallback(
    (input: string) => {
      if (!connectionFlow || !connectionFlow.state.currentApp) return false;

      const { state: flowState } = connectionFlow;

      if (
        flowState.step === "collecting-preauth" ||
        flowState.step === "collecting-form"
      ) {
        // Process the input through connection flow
        connectionFlow.handleUserInput(input);

        return true; // Indicates input was handled by connection flow
      }

      return false; // Input should be handled normally
    },
    [connectionFlow]
  );

  // Get local messages from connection flow
  const localMessages = connectionFlow?.state.localMessages || [];

  // Create enhanced grouped messages that include local messages
  const allGroupedMessages = React.useMemo(() => {
    // Start with the passed groupedMessages
    const grouped: Record<string, ScyraMessage[]> = { ...groupedMessages };

    // Add local messages to the appropriate date groups
    localMessages.forEach((message: ScyraMessage) => {
      const dateKey = moment(message.timestamp).format("YYYY-MM-DD");
      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }

      // Check if message already exists to prevent duplicates
      const messageExists = grouped[dateKey].some(
        (existingMsg) => existingMsg.id === message.id
      );
      if (!messageExists) {
        grouped[dateKey].push(message);
      }
    });

    // Sort messages within each date group
    Object.keys(grouped).forEach((dateKey) => {
      grouped[dateKey].sort(
        (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
      );
    });

    return grouped;
  }, [groupedMessages, localMessages]);

  // Show/hide down-arrow when user scrolls up and track user position
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const atBottom =
        container.scrollHeight - container.scrollTop - container.clientHeight <
        40;
      setShowScrollToBottom(!atBottom);
      setIsUserAtBottom(atBottom);
    };

    container.addEventListener("scroll", handleScroll);
    handleScroll();

    return () => container.removeEventListener("scroll", handleScroll);
  }, [state.messages.length, localMessages.length]);

  // Auto-scroll to bottom only when user is already at bottom and new messages arrive
  useEffect(() => {
    // Only auto-scroll if user is at the bottom or this is the initial load
    if (isUserAtBottom || state.messages.length === 0) {
      scrollToBottom();
    }
  }, [state.messages, localMessages, isUserAtBottom]);

  // Handle loading state changes - only scroll if user is at bottom
  useEffect(() => {
    if (!state.isLoading && isUserAtBottom) {
      // Small delay to ensure DOM is updated after loading completes
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [state.isLoading, isUserAtBottom]);

  // Clear placeholder when connection flow resets
  useEffect(() => {
    if (connectionFlow?.state.step === "idle") {
      setCurrentPlaceholder("");
    }
  }, [connectionFlow?.state.step]);

  // Enhanced ChatInputComponent with connection flow handling
  const EnhancedChatInput = React.useMemo(() => {
    if (!ChatInputComponent) return null;

    // Create a wrapper that handles the enhanced functionality
    const ConnectionAwareChatInput = () => {
      const handleSendMessage = (input: string) => {
        // Check if connection flow should handle this input
        const handledByConnection = handleConnectionInput(input);

        // If not handled by connection flow, call the original sendMessage
        if (!handledByConnection && originalSendMessage) {
          originalSendMessage(input);
        }
      };

      return React.createElement(ChatInputComponent as any, {
        placeholder: currentPlaceholder || "I'm here — whenever you're ready.",
        onSendMessage: handleSendMessage,
        disabled: connectionFlow?.state.isLoading || state.isLoading,
      });
    };

    return <ConnectionAwareChatInput />;
  }, [
    ChatInputComponent,
    currentPlaceholder,
    handleConnectionInput,
    connectionFlow?.state.isLoading,
    state.isLoading,
  ]);

  return (
    <div className="flex relative flex-col h-full">
      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        className="overflow-y-auto flex-1 px-4 py-4 mt-2"
        style={{ minHeight: 0 }}
      >
        {Object.entries(allGroupedMessages).map(([date, messages]) => (
          <div key={date}>
            <div className="mb-2 text-xs font-semibold text-center text-gray-400">
              {moment(date).calendar(null, {
                sameDay: "[Today]",
                lastDay: "[Yesterday]",
                lastWeek: "dddd",
                sameElse: "MMM D, YYYY",
              })}
            </div>
            {messages.map((message) => (
              <ScyraMessageComponent key={message.id} message={message} />
            ))}
          </div>
        ))}

        {/* Typing Indicator */}
        {(state.isLoading || connectionFlow?.state.isLoading) && (
          <TypingIndicator
            agentImageSrc={scyra}
            agentName={capitalizeAgentName(activeAgent || "")}
            message={
              connectionFlow?.state.step === "waiting-auth"
                ? `${capitalizeAgentName(activeAgent || "")} is waiting`
                : undefined
            }
          />
        )}
      </div>

      {/* Floating Down Arrow */}
      {showScrollToBottom && (
        <button
          className="flex absolute right-6 bottom-24 z-20 justify-center items-center w-10 h-10 bg-white rounded-full border border-gray-200 shadow-lg transition hover:bg-gray-50"
          onClick={scrollToBottom}
          aria-label="Scroll to latest message"
        >
          <ArrowDown className="w-6 h-6 text-primary" />
        </button>
      )}

      {/* Chat Input */}
      <div className="flex-shrink-0 px-4 py-4">{EnhancedChatInput}</div>
    </div>
  );
};
