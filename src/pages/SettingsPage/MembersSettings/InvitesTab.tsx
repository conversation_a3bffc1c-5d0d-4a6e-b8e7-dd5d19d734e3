import React, { useState } from 'react';
import { Loader2, Search } from 'lucide-react';
import {
  Invitation,
  MemberRole,
  SuiteMemberRoleApi,
  mapUiRoleToApiRole,
  mapApiRoleToUiRole,
} from '../../../types/members';
import {
  getRoleColor,
  getRoleDisplayName,
  generateMemberInitials,
} from '../../../utils/members';
import DataTable, { Column } from '../../../components/ui/tables/DataTable';
import ActionDropdown from '../../../components/common/ActionDropdown';
import UpdateRoleModal from './UpdateRole';
import CancelInvitationModal from './CancelInvitation';
import { Input } from '@/components/ui';
import {
  useSuiteInvites,
  useUpdateSuiteInviteRoleMutation,
  useCancelSuiteInviteMutation,
  useInviteSuiteMemberMutation,
} from '@/hooks/useMembers';
import { useDebounce } from '@/hooks/useDebounce';
import Pagination from '@/components/common/Pagination';

interface InvitesTabProps {
  agentSuiteKey: string;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onShowAlert: (message: string, type: 'error' | 'success' | 'warning') => void;
}

export const InvitesTab: React.FC<InvitesTabProps> = ({
  agentSuiteKey,
  searchQuery,
  onSearchChange,
  onShowAlert,
}) => {
  const [page, setPage] = useState<number>(1);
  const [pageSize] = useState<number>(10);
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const resendInvitationMutation = useInviteSuiteMemberMutation();

  const [isUpdateRoleModalOpen, setIsUpdateRoleModalOpen] = useState(false);
  const [isCancelInvitationModalOpen, setIsCancelInvitationModalOpen] =
    useState(false);
  const [selectedInvitation, setSelectedInvitation] =
    useState<Invitation | null>(null);

  const { data: invitationsData, isLoading: isLoadingInvitations } =
    useSuiteInvites(
      agentSuiteKey,
      debouncedSearchQuery,
      page,
      pageSize,
      !!agentSuiteKey
    );

  const invitations = invitationsData?.invites || [];

  const updateInvitationRoleMutation = useUpdateSuiteInviteRoleMutation();
  const cancelInvitationMutation = useCancelSuiteInviteMutation();

  const handleUpdateInvitationRole = async (
    invitationId: string,
    newRole: MemberRole
  ) => {
    try {
      await updateInvitationRoleMutation.mutateAsync({
        inviteId: invitationId,
        payload: {
          agentSuiteKey: agentSuiteKey,
          memberRole: mapUiRoleToApiRole(newRole),
        },
      });
      onShowAlert('Role updated successfully', 'success');
      setIsUpdateRoleModalOpen(false);
      setSelectedInvitation(null);
    } catch (error) {
      onShowAlert('Failed to update role', 'error');
      setIsUpdateRoleModalOpen(false);
      setSelectedInvitation(null);
    }
  };

  const handleCancelInvitation = async (invitation: Invitation) => {
    try {
      await cancelInvitationMutation.mutateAsync({
        inviteId: invitation.id,
        agentSuiteKey: agentSuiteKey,
      });
      onShowAlert(
        `Invitation to ${invitation.email} has been cancelled`,
        'success'
      );
    } catch (error) {
      onShowAlert('Failed to cancel invitation', 'error');
    } finally {
      setIsCancelInvitationModalOpen(false);
      setSelectedInvitation(null);
    }
  };

  const resendInvitation = async (invitation: Invitation) => {
    await resendInvitationMutation.mutateAsync({
      agentSuiteKey: agentSuiteKey,
      firstname: invitation.firstname.trim(),
      lastname: invitation.lastname.trim(),
      email: invitation.email.trim(),
      role: invitation.role as SuiteMemberRoleApi,
    });
  };

  // Invitation columns
  const invitationColumns: Column<Invitation>[] = [
    {
      key: 'email',
      label: 'Name',
      render: (_, invitation) => (
        <div className="flex items-center">
          <div className="flex justify-center items-center mr-3 w-10 h-10 text-sm font-medium text-white rounded-full bg-blackOne">
            {generateMemberInitials(invitation.firstname)}
            {generateMemberInitials(invitation.lastname)}
          </div>
          <div>
            <div className="font-medium text-blackOne">
              {invitation.firstname} {invitation.lastname}
            </div>
            <div className="text-sm text-subText">{invitation.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'role',
      label: 'Role',
      render: (_, invitation) => (
        <span
          className={`rounded-lg px-4 h-[34px] w-28 text-xs font-medium flex items-center justify-center ${
            getRoleColor(invitation.role) || 'bg-gray-30 text-white'
          }`}
        >
          {getRoleDisplayName(invitation.role) || 'N/A'}
        </span>
      ),
    },
    {
      key: 'id',
      label: '',
      render: (_, invitation) => {
        const actions = [
          {
            label: 'Change Role',
            icon: null,
            onClick: () => {
              setSelectedInvitation(invitation);
              setIsUpdateRoleModalOpen(true);
            },
            variant: 'default' as const,
          },
          {
            label: resendInvitationMutation.isPending
              ? 'Sending...'
              : resendInvitationMutation.isSuccess
                ? (() => {
                    // Show "Invitation Sent" for 3 seconds, then revert to "Resend Invitation"
                    if (resendInvitationMutation.isSuccess) {
                      setTimeout(() => {
                        resendInvitationMutation.reset &&
                          resendInvitationMutation.reset();
                      }, 3000);
                      return 'Invitation Sent';
                    }
                    return 'Invitation Sent';
                  })()
                : 'Resend Invitation',
            icon: resendInvitationMutation.isPending ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : null,
            onClick: () => {
              resendInvitation(invitation);
            },

            isDisabled: resendInvitationMutation.isPending,
            isLoading: resendInvitationMutation.isPending,
            variant: (() => {
              // Show "Invitation Sent" for 3 seconds, then revert to "Resend Invitation"
              if (resendInvitationMutation.isSuccess) {
                setTimeout(() => {
                  resendInvitationMutation.reset &&
                    resendInvitationMutation.reset();
                }, 3000);
                return 'success' as const;
              }
              return 'default' as const;
            })(),
            closeOnClick: false,
          },
          {
            label: 'Cancel Invitation',
            icon: null,
            onClick: () => {
              setSelectedInvitation(invitation);
              setIsCancelInvitationModalOpen(true);
            },
            variant: 'danger' as const,
          },
        ];

        return <ActionDropdown actions={actions} />;
      },
      className: 'text-right',
    },
  ];

  return (
    <>
      <div className="space-y-6">
        {/* Search */}
        <div className="flex gap-3 items-center">
          <Input
            type="text"
            placeholder="Search by name or email"
            className="py-2 pr-10 pl-4 w-[300px] h-10 text-sm rounded-[10px] border border-[#BAB9B9] placeholder:text-grayTen focus:outline-none focus:ring-0"
            endIcon={<Search className="mt-0.5 w-4 h-4" />}
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>

        {/* Table */}
        <div className="flex overflow-x-auto flex-col gap-4">
          <DataTable
            data={invitations || []}
            columns={invitationColumns}
            loading={isLoadingInvitations}
            emptyMessage="No invitations found"
            rowColoring={true}
            rowColoringType="odd"
            getRowId={(invitation) => invitation.id}
          />
          <Pagination
            currentPage={page}
            totalPages={
              invitationsData
                ? Math.ceil(invitationsData.total / invitationsData.pageSize)
                : 0
            }
            onPageChange={(page) => setPage(page)}
          />
        </div>
      </div>

      {/* Modals */}
      <UpdateRoleModal
        isOpen={isUpdateRoleModalOpen}
        onClose={() => {
          setIsUpdateRoleModalOpen(false);
          setSelectedInvitation(null);
        }}
        onUpdateRole={(newRole: MemberRole) => {
          handleUpdateInvitationRole(selectedInvitation?.id || '', newRole);
        }}
        userDetails={
          selectedInvitation
            ? {
                id: selectedInvitation.id,
                name: `${selectedInvitation.firstname} ${selectedInvitation.lastname}`,
                role: selectedInvitation.role.toLowerCase() as MemberRole,
              }
            : null
        }
        loading={updateInvitationRoleMutation.isPending}
      />

      <CancelInvitationModal
        isOpen={isCancelInvitationModalOpen}
        onClose={() => {
          setIsCancelInvitationModalOpen(false);
          setSelectedInvitation(null);
        }}
        onCancel={handleCancelInvitation}
        invitation={selectedInvitation}
        loading={cancelInvitationMutation.isPending}
      />
    </>
  );
};
