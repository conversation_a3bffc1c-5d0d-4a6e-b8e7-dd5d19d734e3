import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import {
  arrowPrimary,
  chatIcon,
  chatIcon2,
  paperPlane,
} from "../../assets/icons";
import { fullEclipse, regis } from "../../assets/images";
import { ROUTES } from "../../constants/routes";
import { AuthProvider, useAuth } from "../../context/AuthContext";
import { TenantProvider, useTenant } from "../../context/TenantContext";
import { useGetAIAgentSuites } from "@/hooks/useAIAgents";
import { AIAgent } from "@/types/agents";

const AgentPill = ({
  name,
  icon = regis,
  onClick,
}: {
  name: string;
  icon?: string;
  onClick?: () => void;
}) => (
  <div
    className="flex w-fit cursor-pointer items-center gap-2 rounded-lg border border-primary p-1.5 transition-all hover:scale-105"
    onClick={onClick}
  >
    <div className="h-7 w-7 flex-shrink-0">
      <img
        src={icon}
        alt={name}
        className="w-full h-full flex items-center justify-center rounded-full bg-[#718EBF33] object-cover"
      />
    </div>
    <span className="font-medium">{name}</span>
    <img src={chatIcon} alt="chat icon" />
  </div>
);

const AgentFlowIndicator = ({
  agents,
  onAgentClick,
  isPaused,
}: {
  agents: AIAgent[];
  onAgentClick: (agent: AIAgent) => void;
  isPaused: boolean;
}) => {
  if (agents.length === 0) return null;

  // Create duplicated agents for seamless infinite scroll
  const duplicatedAgents = [...agents, ...agents, ...agents];

  const renderAgentWithArrow = (
    agent: AIAgent,
    index: number,
    isLast: boolean
  ) => (
    <div
      key={`${agent.agentKey}-${index}`}
      className="flex flex-shrink-0 items-center py-2"
    >
      <AgentPill
        name={agent.agentName}
        icon={agent.avatar || regis}
        onClick={() => onAgentClick(agent)}
      />
      {!isLast && (
        <img src={arrowPrimary} alt="arrow" className="mx-4 flex-shrink-0" />
      )}
    </div>
  );

  return (
    <div className="relative mt-6 w-full md:w-[438px] overflow-hidden">
      {/* Gradient masks for smooth edges */}
      {/* Left edge */}
      <div className="pointer-events-none absolute left-0 top-0 z-10 h-full w-12 bg-gradient-to-r from-[#fff7f5] to-transparent" />
      {/* Right edge */}
      <div className="pointer-events-none absolute right-0 top-0 z-10 h-full w-12 bg-gradient-to-l from-[#fffefe] to-transparent" />

      <div
        className={`flex items-center gap-0 transition-all ${isPaused ? "[animation-play-state:paused]" : ""} animate-scroll-right`}
        style={{
          width: "max-content",
        }}
      >
        {duplicatedAgents.map((agent, index) =>
          renderAgentWithArrow(
            agent,
            index,
            index === duplicatedAgents.length - 1
          )
        )}
      </div>
    </div>
  );
};

const HeroSectionContent = () => {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const { data: agentSuites = [] } = useGetAIAgentSuites();

  // Combine all agents from all suites and add hardcoded Regis agent
  const allAgents: AIAgent[] = [
    ...agentSuites.flatMap((suite) => suite.availableAgents || []),
    {
      agentName: "Regis",
      agentKey: "regis",
      description: "Registration & Access Assistance",
      roleDescription: "I handle account setup and resolve access issues",
      avatar: regis,
      roles: ["Account setup, simplify login", "Resolve access issues"],
      categories: ["ACCESS_SERVICES"],
      enabled: true,
    },
  ];

  const { setActiveAgent } = useTenant();
  const [selectedAgent, setSelectedAgent] = useState<AIAgent>(
    allAgents.find((agent) => agent.agentKey === "regis") || allAgents[0]
  );
  const [isPaused, setIsPaused] = useState(false);
  const [userMessage, setUserMessage] = useState("");

  // Update selected agent when agents are loaded
  useEffect(() => {
    if (allAgents.length > 0 && !selectedAgent) {
      const regisAgent = allAgents.find((agent) => agent.agentKey === "regis");
      setSelectedAgent(regisAgent || allAgents[0]);
    }
  }, [allAgents, selectedAgent]);

  const handleAgentClick = (agent: AIAgent) => {
    setSelectedAgent(agent);
    setIsPaused(true);
    setActiveAgent(agent.agentKey);
    // Resume scrolling after 3 seconds
    setTimeout(() => setIsPaused(false), 3000);
  };

  const handleInputFocus = () => {
    setIsPaused(true);
  };

  const handleInputBlur = () => {
    setIsPaused(false);
  };

  const handleSendMessage = async () => {
    if (!userMessage.trim()) return;

    if (selectedAgent?.agentKey === "regis") {
      setActiveAgent("regis");

      if (isAuthenticated) {
        navigate(ROUTES.DASHBOARD_AI_AGENTS);
      } else {
        navigate(ROUTES.SIGNUP);
      }
    } else {
      // Set the selected agent as active before navigation
      if (selectedAgent?.agentKey) {
        setActiveAgent(selectedAgent.agentKey);
      }

      // Find the suite that contains this agent
      const suite = agentSuites.find((s) =>
        s.availableAgents?.some((a) => a.agentKey === selectedAgent?.agentKey)
      );
      if (suite) {
        navigate(ROUTES.AGENTS(suite.agentSuiteKey), {
          state: { selectedAgent, userMessage },
        });
      }
    }
  };

  const handleButtonClick = async () => {
    setActiveAgent("regis");

    if (isAuthenticated) {
      navigate(ROUTES.DASHBOARD_AI_AGENTS, {
        state: { selectedAgent: "regis" },
      });
    } else {
      navigate(ROUTES.SIGNUP);
    }
  };

  return (
    <section className="relative overflow-hidden">
      {/* Background Ball */}
      <div
        className="pointer-events-none absolute right-0 top-0 z-0 mt-12 h-16 w-16 md:h-[108px] md:w-[108px]"
        style={{
          backgroundImage: `url(${fullEclipse})`,
          backgroundSize: "contain",
          backgroundPosition: "right top",
          backgroundRepeat: "no-repeat",
          transform: "translate(15%, -30%)",
        }}
      />

      <div className="relative z-10 mx-auto flex max-w-screen-2xl flex-col items-center px-4 py-24 sm:px-6 md:flex-row md:gap-8 lg:px-8">
        {/* LHS - Content */}
        <div className="md:min-w-1/2 w-full text-center md:w-1/2 md:text-left">
          <h1 className="mb-6 text-4xl font-semibold leading-normal text-gray-900 md:text-[56px]">
            The Agentic AI
            <br />
            Transformation Hub
          </h1>
          <p className="mb-8 max-w-[627px] font-inter text-lg text-gray-700">
            Orchestrate, evolve, and deploy AI agents across your enterprise.
            <br />
            Agentous PivoTL is the transformation layer that deploys autonomous
            agentic teams to manage critical business functions and accelerate
            value creation.
          </p>
          <div className="flex flex-col justify-center gap-4 px-4 sm:flex-row md:justify-start md:px-0">
            <button
              onClick={handleButtonClick}
              className="rounded-md bg-primary px-8 py-[9px] font-medium text-white transition-colors hover:bg-orange-15"
            >
              <div className="flex items-center justify-center gap-3">
                <img src={chatIcon2} alt="" />
                <span className="mt-0.5">Chat with Regis</span>
              </div>
            </button>
          </div>
        </div>

        {/* RHS - Interactive agents interface */}
        <div className="w-full md:w-1/2">
          <div className="flex items-center justify-center pt-10 font-inter md:pt-0">
            <div className="w-full md:w-[438px]">
              <div className="relative rounded-2xl bg-white px-4 py-6 shadow transition-all duration-300 hover:border hover:border-primary/20 hover:shadow-[0_0_20px_rgba(255,107,53,0.15)]">
                <div className="mb-6 flex gap-3">
                  {/* Selected agent's image */}
                  <div className="h-12 w-12 flex-shrink-0">
                    <div className="rounded-full bg-grayTwentySix">
                      <img
                        src={selectedAgent?.avatar || regis}
                        alt={selectedAgent?.agentName || "Agent"}
                        className="h-full w-full rounded-full object-cover"
                      />
                    </div>
                  </div>

                  <div className="flex-1">
                    <div className="mb-1 flex items-center gap-2">
                      {/* Selected agent's name */}
                      <span className="font-semibold text-darkGray">
                        {selectedAgent?.agentName || "Agent"}
                      </span>
                    </div>
                    <div className="rounded-lg bg-gray-5 p-3 font-medium text-grayTwentyFour">
                      {/* Selected agent's description */}
                      <div className="w-fit">
                        <span>
                          {selectedAgent?.description ||
                            "Hi, I'm ready to get started!"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  className="flex h-10 cursor-pointer items-center justify-between rounded-md border border-grayNine pl-2 pr-4 text-grayTen transition-colors hover:border-primary"
                  onClick={() => {
                    const input = document.querySelector(
                      'input[type="text"]'
                    ) as HTMLInputElement;
                    if (input) {
                      input.focus();
                      handleInputFocus();
                    }
                  }}
                >
                  <input
                    type="text"
                    value={userMessage}
                    onChange={(e) => setUserMessage(e.target.value)}
                    onFocus={handleInputFocus}
                    onBlur={handleInputBlur}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleSendMessage();
                      }
                    }}
                    className="flex-1 bg-transparent outline-none [border:none] [box-shadow:none] placeholder:text-grayTen focus:[border:none] focus:[box-shadow:none] active:[border:none]"
                    placeholder="I'm here — whenever you're ready."
                  />
                  <img
                    src={paperPlane}
                    alt="send message"
                    className="cursor-pointer transition-opacity hover:opacity-70"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSendMessage();
                    }}
                  />
                </div>
              </div>

              {/* Agents carousel */}
              <AgentFlowIndicator
                agents={allAgents}
                onAgentClick={handleAgentClick}
                isPaused={isPaused}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export const HeroSection = () => {
  return (
    <TenantProvider>
      <AuthProvider>
        <HeroSectionContent />
      </AuthProvider>
    </TenantProvider>
  );
};
