import EnhancedChatSidebar from "@/components/common/EnhancedChatSidebar";
import { useState } from "react";

const Marketplace = () => {
  const [chatMessage, setChatMessage] = useState<string>("");

  return (
    <div className="max-h-screen font-inter">
      <div className="h-[calc(100vh-100px)] flex overflow-hidden flex-1">
        {/* Chat Sidebar */}
        <EnhancedChatSidebar
          externalMessage={chatMessage}
        />

        <div>Broooo</div>
      </div>
    </div>
  );
};

export default Marketplace;
