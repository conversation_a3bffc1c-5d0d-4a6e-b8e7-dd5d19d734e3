import { useEffect, useState } from "react";
import Marketplace from "./Marketplace";
import AgentDetails from "./AgentDetails";
import { motion } from "framer-motion";

export type AgentDetailsStep = "marketplace" | "details";

interface AgentDetailsState {
  step: AgentDetailsStep;
}

const AgentDetailsPage = () => {
  // Ensure the overall page is scrolled to top on initial navigation to this page.
  useEffect(() => {
    // Use a setTimeout to allow the route transition to complete before forcing scroll.
    const id = window.setTimeout(() => {
      try {
        window.scrollTo({ top: 0, left: 0, behavior: "auto" });
      } catch (e) {
        window.scrollTo(0, 0);
      }
    }, 0);

    return () => window.clearTimeout(id);
  }, []);

  const [state, setState] = useState<AgentDetailsState>({
    step: "marketplace",
  });

  // State update helper
  const updateState = (updates: Partial<AgentDetailsState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  };

  const handleNext = () => {
    if (state.step === "marketplace") {
      updateState({ step: "details" });
    }
  };

  const handleBack = () => {
    if (state.step === "details") {
      updateState({ step: "marketplace" });
    }
  };

  const renderStep = () => {
    switch (state.step) {
      case "marketplace":
        return <Marketplace />;
      case "details":
        return <AgentDetails />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-full">
      <motion.div
        key={state.step}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex h-full">{renderStep()}</div>
      </motion.div>
    </div>
  );
};

export default AgentDetailsPage;
