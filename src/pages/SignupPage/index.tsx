import { useRegisChat } from "../../hooks/useRegisChat";
import { RegisChatInterface } from "../../components/chat/RegisChatInterface";
import { ChatInput } from "../../components/chat/ChatInput";
import { VerificationCodeInput } from "../../components/chat/VerificationCodeInput";
import { useState, useMemo } from "react";
import {
  ArrowUpFromDot,
  Check,
  X,
  Eye,
  EyeOff,
  ChevronRight,
} from "lucide-react";
import { eclipse, halfEclipse } from "../../assets/images";
import { Spinner } from "@/components/common/Loader";
import { motion } from "framer-motion";
import { ROUTES } from "../../constants/routes";
import { useNavigate } from "react-router-dom";

interface PasswordValidation {
  minLength: boolean;
  hasLowercase: boolean;
  hasUppercase: boolean;
  hasDigit: boolean;
  hasSpecialChar: boolean;
}

const sidebarItems = [
  {
    path: ROUTES.AGENTS("set-iq"),
    label: "AI Agents",
  },
  {
    path: ROUTES.PRICING,
    label: "Pricing",
  },
];

const validatePassword = (password: string): PasswordValidation => {
  return {
    minLength: password.length >= 8,
    hasLowercase: /[a-z]/.test(password),
    hasUppercase: /[A-Z]/.test(password),
    hasDigit: /\d/.test(password),
    hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
  };
};

const PasswordValidationRules = ({ password }: { password: string }) => {
  const validation = validatePassword(password);

  const rules = [
    {
      key: "minLength",
      label: "At least 8 characters",
      valid: validation.minLength,
    },
    {
      key: "hasLowercase",
      label: "One lowercase letter",
      valid: validation.hasLowercase,
    },
    {
      key: "hasUppercase",
      label: "One uppercase letter",
      valid: validation.hasUppercase,
    },
    { key: "hasDigit", label: "One digit", valid: validation.hasDigit },
    {
      key: "hasSpecialChar",
      label: "One special character",
      valid: validation.hasSpecialChar,
    },
  ];

  return (
    <div className="mt-2 space-y-1 text-gray-600">
      <p className="text-xs">Password must contain:</p>
      {rules.map((rule) => (
        <div key={rule.key} className="flex items-center space-x-2">
          {rule.valid ? (
            <Check className="w-3 h-3 text-successTwo" />
          ) : (
            <X className="w-3 h-3 text-red-500" />
          )}
          <span
            className={`text-xs ${rule.valid ? "text-successTwo" : "text-red-600"}`}
          >
            {rule.label}
          </span>
        </div>
      ))}
    </div>
  );
};

const SignupFormSidebar = ({
  state,
  sendMessage,
  stepConfig,
  inputValue,
  setInputValue,
  password,
}: any) => {
  const [showPassword, setShowPassword] = useState(false);
  const [firstName, setFirstName] = useState(
    state.onboardingData.firstName || ""
  );
  const [lastName, setLastName] = useState(state.onboardingData.lastName || "");
  const [email, setEmail] = useState(state.onboardingData.email || "");
  const [fieldErrors, setFieldErrors] = useState<{ [key: string]: boolean }>(
    {}
  );

  const handleSubmit = () => {
    if (inputValue.trim()) {
      sendMessage(inputValue.trim());
      setInputValue("");
    }
  };

  const handleMultiFieldSubmit = () => {
    const errors: { [key: string]: boolean } = {};

    if (!firstName.trim()) errors.firstName = true;
    if (!lastName.trim()) errors.lastName = true;
    if (!email.trim()) errors.email = true;

    setFieldErrors(errors);

    if (Object.keys(errors).length === 0) {
      const message = `My first name is ${firstName}, last name is ${lastName} and email is ${email}`;
      sendMessage(message);
      setFieldErrors({});
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (["firstName", "lastName", "email"].includes(state.currentStep)) {
        handleMultiFieldSubmit();
      } else {
        handleSubmit();
      }
    }
  };

  const renderFormContent = () => {
    switch (state.currentStep) {
      case "firstName":
      case "lastName":
      case "email":
        return (
          <div className="space-y-4">
            <div className="text-center text-lg font-medium">Sign Up</div>
            {/* First Name Field */}
            <div className="relative">
              <input
                type="text"
                value={firstName}
                onChange={(e) => {
                  setFirstName(e.target.value);
                  if (fieldErrors.firstName) {
                    setFieldErrors((prev) => ({ ...prev, firstName: false }));
                  }
                }}
                onKeyDown={handleKeyDown}
                disabled={state.isLoading}
                className={`h-12 w-full rounded-md border px-3 py-2 text-sm text-blackTwo transition-all focus:outline-none focus:ring-2 focus:ring-primary/20 disabled:cursor-not-allowed disabled:opacity-50 ${
                  fieldErrors.firstName
                    ? "border-primary focus:border-primary"
                    : "border-gray-300 focus:border-primary"
                }`}
                placeholder="First name *"
              />
            </div>

            {/* Last Name Field */}
            <div className="relative">
              <input
                type="text"
                value={lastName}
                onChange={(e) => {
                  setLastName(e.target.value);
                  if (fieldErrors.lastName) {
                    setFieldErrors((prev) => ({ ...prev, lastName: false }));
                  }
                }}
                onKeyDown={handleKeyDown}
                disabled={state.isLoading}
                className={`h-12 w-full rounded-md border px-3 py-2 text-sm text-blackTwo transition-all focus:outline-none focus:ring-2 focus:ring-primary/20 disabled:cursor-not-allowed disabled:opacity-50 ${
                  fieldErrors.lastName
                    ? "border-primary focus:border-primary"
                    : "border-gray-300 focus:border-primary"
                }`}
                placeholder="Last name *"
              />
            </div>

            {/* Email Field */}
            <div className="relative">
              <input
                type="email"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  if (fieldErrors.email) {
                    setFieldErrors((prev) => ({ ...prev, email: false }));
                  }
                }}
                onKeyDown={handleKeyDown}
                disabled={state.isLoading}
                className={`h-12 w-full rounded-md border px-3 py-2 text-sm text-blackTwo transition-all focus:outline-none focus:ring-2 focus:ring-primary/20 disabled:cursor-not-allowed disabled:opacity-50 ${
                  fieldErrors.email
                    ? "border-primary focus:border-primary"
                    : "border-gray-300 focus:border-primary"
                }`}
                placeholder="Email address *"
              />
            </div>

            <div className="pt-2">
              <hr className="border-primary" />
            </div>

            <div className="text-sm text-gray-700">
              By creating an account, you agree to Agentous'{" "}
              <span className="cursor-pointer text-primary underline hover:text-darkOrangeTwo">
                Terms and Conditions
              </span>
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleMultiFieldSubmit}
                disabled={state.isLoading}
                className="flex w-fit items-center gap-2 rounded-md border border-primary bg-lightOrangeTwo px-6 py-2 font-medium text-blue-midnight transition-colors hover:border-lightOrangeTwo hover:bg-orange-15 hover:text-white disabled:cursor-not-allowed disabled:border-none disabled:bg-gray-400 disabled:text-whiteOff disabled:opacity-50"
              >
                <span>Proceed</span>
                <ArrowUpFromDot className="w-5 rotate-90" />
              </button>
            </div>
          </div>
        );

      case "verification":
        return (
          <div className="space-y-4">
            <div className="text-center text-lg font-medium">Sign Up</div>
            <VerificationCodeInput
              email={state.onboardingData.email}
              onResend={() =>
                sendMessage(
                  "I did not get the verification code, please resend it."
                )
              }
              onComplete={(code) => sendMessage(code)}
            />
          </div>
        );

      case "password":
        return (
          <div className="space-y-4">
            <div className="text-center text-lg font-medium">Sign Up</div>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                disabled={state.isLoading}
                className="h-12 w-full rounded-md border border-gray-300 px-3 py-2 pr-10 text-sm text-blackTwo transition-all focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder={stepConfig.placeholder}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-primary"
                disabled={state.isLoading}
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
            </div>
            <PasswordValidationRules password={password} />
            <div className="py-2">
              <hr className="border-primary" />
            </div>
            <div className="flex justify-end">
              <button
                onClick={handleSubmit}
                disabled={!inputValue.trim() || state.isLoading}
                className="flex w-fit items-center gap-2 rounded-md border border-primary bg-lightOrangeTwo px-6 py-2 font-medium text-blue-midnight transition-colors hover:border-lightOrangeTwo hover:bg-orange-15 hover:text-white disabled:cursor-not-allowed disabled:border-none disabled:bg-gray-400 disabled:text-whiteOff disabled:opacity-50"
              >
                <span>Proceed</span>
                <ArrowUpFromDot className="w-5 rotate-90" />
              </button>
            </div>
          </div>
        );

      case "completed":
        return (
          <div className="text-center">
            <div className="rounded-lg border border-peachTwo bg-white px-6 py-12">
              <h3 className="mb-2 text-lg font-semibold text-primary">
                Account Created Successfully!
              </h3>
              <p className="text-sm text-blackTwo">
                Redirecting you to the dashboard...
              </p>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center text-gray-500">
            <p className="mb-4">Getting started...</p>
            <Spinner />
          </div>
        );
    }
  };

  return (
    <div className="relative flex h-screen flex-col justify-center overflow-hidden rounded bg-gradient-to-br from-orange-50/50 to-orange-100 p-6 font-inter">
      {/* <div className="absolute right-24 top-8 z-0">
        <button
          onClick={login}
          className="rounded-lg bg-primary px-8 py-[9px] font-semibold text-white transition-colors hover:bg-orange-15"
        >
          Login
        </button>
      </div> */}

      {/* Background Objects */}
      <div
        className="pointer-events-none absolute inset-0 z-0 -mt-48 bg-[right] bg-no-repeat"
        style={{
          backgroundImage: `url(${halfEclipse})`,
          backgroundSize: "auto",
        }}
      />
      <div
        className="pointer-events-none absolute inset-0 z-0 bg-[right_bottom] bg-no-repeat"
        style={{
          backgroundImage: `url(${eclipse})`,
          backgroundSize: "auto",
        }}
      />

      <div className="relative z-10 max-h-full overflow-y-auto rounded-2xl bg-white p-6 shadow-sm">
        {renderFormContent()}
      </div>
    </div>
  );
};

export const PivotlSignupPage = () => {
  const { state, sendMessage } = useRegisChat("signup");
  const [password, setPassword] = useState("");
  const [currentValue, setCurrentValue] = useState("");
  const navigate = useNavigate();

  // Get current step configuration
  const getCurrentStepConfig = () => {
    switch (state.currentStep) {
      case "firstName":
        return {
          placeholder: "Enter your first name",
          chatPlaceholder: "Type your first name here...",
          type: "text" as const,
        };
      case "lastName":
        return {
          placeholder: "Enter your last name",
          chatPlaceholder: "Type your last name here...",
          type: "text" as const,
        };
      case "email":
        return {
          placeholder: "Enter your email address",
          chatPlaceholder: "Type your email address here...",
          type: "email" as const,
        };
      case "verification":
        return {
          placeholder: "Enter verification code",
          chatPlaceholder: "Type the 6-digit verification code...",
          type: "text" as const,
        };
      case "password":
        return {
          placeholder: "Enter your password",
          chatPlaceholder: "Type your password here...",
          type: "password" as const,
          showPasswordToggle: true,
        };
      default:
        return {
          placeholder: "",
          chatPlaceholder: "I'm here — whenever you're ready.",
          type: "text" as const,
        };
    }
  };

  const stepConfig = getCurrentStepConfig();
  const inputValue = state.currentStep === "password" ? password : currentValue;
  const setInputValue =
    state.currentStep === "password" ? setPassword : setCurrentValue;

  // Memoized ChatInput component to prevent focus loss
  const MemoizedChatInput = useMemo(() => {
    return () => (
      <ChatInput
        onSendMessage={sendMessage}
        placeholder={stepConfig.chatPlaceholder}
        disabled={state.isLoading}
      />
    );
  }, [sendMessage, stepConfig.chatPlaceholder, state.isLoading]);

  return (
    <div className="mx-auto max-w-screen-3xl font-inter">
      <div className="grid h-screen grid-cols-1 lg:grid-cols-3 border-t border-[#E6E6E6]">
        {/* LHS - Chat Interface */}
        <div className="relative lg:col-span-2 lg:px-10">
          <div className="flex h-[calc(100vh-180px)] md:h-[calc(100vh-100px)] bg-white">
            <motion.nav
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="space-y-4 border-gray-200"
            >
              {/* Navigation Items */}
              <div className="mt-32 h-full w-32 flex-1 space-y-4">
                {sidebarItems.map((item, index) => (
                  <motion.button
                    key={item.label}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: (index + 1) * 0.1 }}
                    onClick={() => navigate(item.path)}
                    className={`group flex h-10 w-fit items-center rounded-2xl px-3 transition-colors hover:bg-[#FFECE3]/80`}
                    title={item.label}
                  >
                    <span className="text-sm font-medium text-subText">
                      {item.label}
                    </span>

                    <ChevronRight className="hidden h-5 w-5 text-primary group-hover:block" />
                  </motion.button>
                ))}
              </div>
            </motion.nav>

            <div className="border-l border-peach-5 w-full">
              <RegisChatInterface
                state={state}
                ChatInputComponent={MemoizedChatInput}
              />
            </div>
          </div>
        </div>

        {/* RHS - Form Sidebar */}
        <div className="lg:col-span-1">
          <SignupFormSidebar
            state={state}
            sendMessage={sendMessage}
            stepConfig={stepConfig}
            inputValue={inputValue}
            setInputValue={setInputValue}
            password={password}
          />
        </div>
      </div>
    </div>
  );
};

export default PivotlSignupPage;
