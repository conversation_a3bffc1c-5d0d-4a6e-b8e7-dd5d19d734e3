import React, { useRef, useState } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { Search, Filter } from 'lucide-react';
import { motion } from 'framer-motion';
import AnimatedTabs, { Tab } from '@/components/ui/AnimatedTabs';
import SuiteAgentDropdown from '@/pages/DashboardPage/AnalyticsDashboard/SuiteAgentDropdowns';
import { ROUTES } from '@/constants/routes';
import { Input } from '@/components/ui';
import CustomDropdown from '@/components/common/CustomDropdown';
import DateRangePicker from '@/components/ui/DateRangePicker';

interface DateRange {
  from?: Date;
  to?: Date;
}

const AnalyticsDashboard: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);
  
  // Date range picker state
  const [showDateRangePicker, setShowDateRangePicker] = useState(false);
  const [currentDateRangeType, setCurrentDateRangeType] = useState<string>('');
  
  // Get search params from URL
  const currentSearchParams = new URLSearchParams(location.search);
  const currentSearchTerm = currentSearchParams.get('search') || '';
  
  const getSelectedFilters = () => {
    const filters = [];
    
    // Only show filters relevant to the current tab
    if (activeTab === 'insights') {
      if (currentSearchParams.get('from') || currentSearchParams.get('to')) filters.push('date');
    } else if (activeTab === 'task-logs') {
      if (currentSearchParams.get('status')) filters.push('status');
      if (currentSearchParams.get('priority')) filters.push('priority');
      if (currentSearchParams.get('from') || currentSearchParams.get('to')) filters.push('date');
    } else if (activeTab === 'assignment-logs') {
      if (currentSearchParams.get('assignedDateFrom') || currentSearchParams.get('assignedDateTo')) filters.push('assignment-date');
      if (currentSearchParams.get('startDateFrom') || currentSearchParams.get('startDateTo')) filters.push('start-date');
      if (currentSearchParams.get('status')) filters.push('status');
    }
    
    return filters;
  };

  const handleSearchChange = (value: string) => {
    const activeTab = getActiveTabFromPath();
    if (activeTab === 'insights') return; // No search for insights

    // Clear existing timeout
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    const newSearchParams = new URLSearchParams(location.search);
    newSearchParams.set('search', value);

    const newUrl = `${location.pathname}?${newSearchParams.toString()}`;
    navigate(newUrl, { replace: true });
  };

  const tabs: Tab[] = [
    {
      id: 'insights',
      label: 'Insights',
    },
    {
      id: 'task-logs',
      label: 'Task Logs',
    },
    {
      id: 'assignment-logs',
      label: 'Assignment Logs',
    },
  ];

  // Determine active tab based on current path
  const getActiveTabFromPath = () => {
    const path = location.pathname;
    if (path.includes('/task-logs')) return 'task-logs';
    if (path.includes('/assignment-logs')) return 'assignment-logs';
    return 'insights'; // Default to insights
  };

  const activeTab = getActiveTabFromPath();

  const insightFilterOptions = [{ id: 'date', label: 'Date', value: 'date' }];

  const taskLogFilterOptions = [
    { id: 'date', label: 'Date', value: 'date' },
    { id: 'status', label: 'Status', value: 'status' },
    { id: 'priority', label: 'Priority', value: 'priority' },
  ];

  const assignmentLogFilterOptions = [
    { id: '1', label: 'Assignment Date', value: 'assignment-date' },
    { id: '2', label: 'Start Date', value: 'start-date' },
    { id: '3', label: 'Status', value: 'status' },
  ];

  const options =
    activeTab === 'insights'
      ? insightFilterOptions
      : activeTab === 'task-logs'
        ? taskLogFilterOptions
        : assignmentLogFilterOptions;

  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [showPriorityDropdown, setShowPriorityDropdown] = useState(false);

  const handleFilterSelection = (selectedValues: string[]) => {
    const selectedValue = selectedValues[0]; // Get the first selected value
    
    if (selectedValue === 'date' || selectedValue === 'assignment-date' || selectedValue === 'start-date') {
      // Open date range picker for date-related filters
      setCurrentDateRangeType(selectedValue);
      setShowDateRangePicker(true);
    } else if (selectedValue === 'status') {
      // Show status selection dropdown
      setShowStatusDropdown(true);
    } else if (selectedValue === 'priority') {
      // Show priority selection dropdown
      setShowPriorityDropdown(true);
    }
  };

  // Status options for task logs and assignment logs
  const statusOptions = [
    { id: 'pending', label: 'Pending', value: 'PENDING' },
    { id: 'in-progress', label: 'In Progress', value: 'IN_PROGRESS' },
    { id: 'completed', label: 'Completed', value: 'COMPLETED' },
    { id: 'cancelled', label: 'Cancelled', value: 'CANCELLED' },
  ];

  // Priority options for task logs
  const priorityOptions = [
    { id: 'low', label: 'Low', value: 'LOW' },
    { id: 'medium', label: 'Medium', value: 'MEDIUM' },
    { id: 'high', label: 'High', value: 'HIGH' },
  ];

  const handleStatusSelection = (selectedValues: string[]) => {
    const newSearchParams = new URLSearchParams(location.search);
    
    if (selectedValues.length > 0) {
      newSearchParams.set('status', selectedValues.join(','));
    } else {
      newSearchParams.delete('status');
    }
    
    navigate(`${location.pathname}?${newSearchParams.toString()}`, { replace: true });
    setShowStatusDropdown(false);
  };

  const handlePrioritySelection = (selectedValues: string[]) => {
    const newSearchParams = new URLSearchParams(location.search);
    
    if (selectedValues.length > 0) {
      newSearchParams.set('priority', selectedValues.join(','));
    } else {
      newSearchParams.delete('priority');
    }
    
    navigate(`${location.pathname}?${newSearchParams.toString()}`, { replace: true });
    setShowPriorityDropdown(false);
  };

  // Get current status/priority selections for display
  const getCurrentStatusSelections = () => {
    const statusParam = currentSearchParams.get('status');
    return statusParam ? statusParam.split(',') : [];
  };

  const getCurrentPrioritySelections = () => {
    const priorityParam = currentSearchParams.get('priority');
    return priorityParam ? priorityParam.split(',') : [];
  };

  // Clear all filters function
  const clearAllFilters = () => {
    const newSearchParams = new URLSearchParams();
    
    // Keep only suite and agent parameters
    const suite = currentSearchParams.get('suite');
    const agent = currentSearchParams.get('agent');
    
    if (suite) newSearchParams.set('suite', suite);
    if (agent) newSearchParams.set('agent', agent);
    
    navigate(`${location.pathname}?${newSearchParams.toString()}`, { replace: true });
  };

  // Check if any filters are active
  const hasActiveFilters = () => {
    const filters = getSelectedFilters();
    return filters.length > 0 || currentSearchTerm.length > 0;
  };

  const handleDateRangeApply = (range: { from?: Date; to?: Date }) => {
    const newSearchParams = new URLSearchParams(location.search);
    
    if (currentDateRangeType === 'date') {
      // Task logs date range
      if (range.from) newSearchParams.set('from', range.from.toISOString());
      if (range.to) newSearchParams.set('to', range.to.toISOString());
    } else if (currentDateRangeType === 'assignment-date') {
      // Assignment date range
      if (range.from) newSearchParams.set('assignedDateFrom', range.from.toISOString());
      if (range.to) newSearchParams.set('assignedDateTo', range.to.toISOString());
    } else if (currentDateRangeType === 'start-date') {
      // Start date range
      if (range.from) newSearchParams.set('startDateFrom', range.from.toISOString());
      if (range.to) newSearchParams.set('startDateTo', range.to.toISOString());
    }
    
    navigate(`${location.pathname}?${newSearchParams.toString()}`, { replace: true });
    setShowDateRangePicker(false);
  };

  const getCurrentDateRange = () => {
    let from: Date | undefined, to: Date | undefined;
    
    if (currentDateRangeType === 'date') {
      from = currentSearchParams.get('from') ? new Date(currentSearchParams.get('from')!) : undefined;
      to = currentSearchParams.get('to') ? new Date(currentSearchParams.get('to')!) : undefined;
    } else if (currentDateRangeType === 'assignment-date') {
      from = currentSearchParams.get('assignedDateFrom') ? new Date(currentSearchParams.get('assignedDateFrom')!) : undefined;
      to = currentSearchParams.get('assignedDateTo') ? new Date(currentSearchParams.get('assignedDateTo')!) : undefined;
    } else if (currentDateRangeType === 'start-date') {
      from = currentSearchParams.get('startDateFrom') ? new Date(currentSearchParams.get('startDateFrom')!) : undefined;
      to = currentSearchParams.get('startDateTo') ? new Date(currentSearchParams.get('startDateTo')!) : undefined;
    }
    
    return { from, to };
  };

  // Check if we're on a child route (detail page)
  const isChildRoute =
    location.pathname.includes('/all') ||
    (location.pathname !== ROUTES.DASHBOARD_ANALYTICS_INSIGHTS &&
      location.pathname !== ROUTES.DASHBOARD_ANALYTICS_TASK_LOGS &&
      location.pathname !== ROUTES.DASHBOARD_ANALYTICS_ASSIGNMENT_LOGS);

  const handleTabChange = (tabId: string) => {
    // Clear page-specific filters when switching tabs, but keep suite/agent
    const newSearchParams = new URLSearchParams();
    
    // Keep only suite and agent parameters from current URL
    const currentParams = new URLSearchParams(location.search);
    const suite = currentParams.get('suite');
    const agent = currentParams.get('agent');
    
    if (suite) newSearchParams.set('suite', suite);
    if (agent) newSearchParams.set('agent', agent);

    switch (tabId) {
      case 'insights':
        navigate(
          `${ROUTES.DASHBOARD_ANALYTICS_INSIGHTS}?${newSearchParams.toString()}`
        );
        break;
      case 'task-logs':
        navigate(
          `${ROUTES.DASHBOARD_ANALYTICS_TASK_LOGS}?${newSearchParams.toString()}`
        );
        break;
      case 'assignment-logs':
        navigate(
          `${ROUTES.DASHBOARD_ANALYTICS_ASSIGNMENT_LOGS}?${newSearchParams.toString()}`
        );
        break;
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="overflow-y-auto flex-1">
        {/* Conditionally render header and tabs only on parent pages */}
        {!isChildRoute && (
          <div className="p-6 space-y-6">
            <div className="flex justify-between items-center w-full">
              {/* Header with Dropdowns */}
              <div className="p-4 bg-white rounded-lg">
                <SuiteAgentDropdown />
              </div>

              {/* Search and Filter */}

              <div className="flex gap-3 justify-end items-center">
                {activeTab !== 'insights' && (
                  <Input
                    type="text"
                    placeholder="Search"
                    value={currentSearchTerm}
                    onChange={(e) => handleSearchChange(e.target.value)}
                    className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] p-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
                    endIcon={<Search className="w-5 h-5" />}
                  />
                )}
                <CustomDropdown
                  options={options}
                  selectedValues={getSelectedFilters()}
                  onSelectionChange={handleFilterSelection}
                  buttonText="Filter"
                  isMultiSelect={false}
                />
                {hasActiveFilters() && (
                  <button
                    onClick={clearAllFilters}
                    className="px-4 py-2 text-red-600 hover:text-red-700 text-sm font-medium border border-red-200 rounded-lg hover:bg-red-50 transition-colors"
                  >
                    Clear All
                  </button>
                )}
              </div>
            </div>

            {/* Tabs */}
            <AnimatedTabs
              tabs={tabs}
              activeTab={activeTab}
              onTabChange={handleTabChange}
              showContent={false}
            />
          </div>
        )}

        {/* Content */}
        <div className="flex-1">
          <Outlet />
        </div>
      </div>
      
      {/* Date Range Picker Modal */}
      <DateRangePicker
        isOpen={showDateRangePicker}
        onClose={() => setShowDateRangePicker(false)}
        onApply={handleDateRangeApply}
        initialRange={getCurrentDateRange()}
      />

      {/* Status Selection Modal */}
      {showStatusDropdown && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          onClick={() => setShowStatusDropdown(false)}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-2xl p-6 shadow-2xl max-w-md w-full mx-4"
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            <h3 className="text-lg font-semibold mb-4">Select Status</h3>
            <CustomDropdown
              options={statusOptions}
              selectedValues={getCurrentStatusSelections()}
              onSelectionChange={handleStatusSelection}
              buttonText="Select Status"
              isMultiSelect={true}
            />
            <div className="flex gap-3 mt-6">
              <button
                onClick={() => setShowStatusDropdown(false)}
                className="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  handleStatusSelection([]);
                  setShowStatusDropdown(false);
                }}
                className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Clear All
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Priority Selection Modal */}
      {showPriorityDropdown && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          onClick={() => setShowPriorityDropdown(false)}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-2xl p-6 shadow-2xl max-w-md w-full mx-4"
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            <h3 className="text-lg font-semibold mb-4">Select Priority</h3>
            <CustomDropdown
              options={priorityOptions}
              selectedValues={getCurrentPrioritySelections()}
              onSelectionChange={handlePrioritySelection}
              buttonText="Select Priority"
              isMultiSelect={true}
            />
            <div className="flex gap-3 mt-6">
              <button
                onClick={() => setShowPriorityDropdown(false)}
                className="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  handlePrioritySelection([]);
                  setShowPriorityDropdown(false);
                }}
                className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Clear All
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
};

export default AnalyticsDashboard;
