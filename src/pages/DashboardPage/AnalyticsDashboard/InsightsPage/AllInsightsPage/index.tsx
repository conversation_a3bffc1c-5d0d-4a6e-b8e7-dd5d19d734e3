import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ChevronLeft, Filter, Search } from 'lucide-react';
import { Icons } from '@/assets/icons/DashboardIcons';
import { Button } from '@/components/ui/Button';
import Pagination from '@/components/common/Pagination';
import { useAnalyticsParams } from '@/utils/urlParams';
import { useTenant } from '@/context/TenantContext';
import { useDailyInsightsList } from '@/hooks/useDashboard';

interface InsightData {
  id: string;
  insight: string;
  type: 'warning' | 'info' | 'suggestion';
  timestamp: string;
}

const InsightItem: React.FC<InsightData> = ({ insight, timestamp }) => {
  return (
    <div className="flex justify-between items-center p-4 bg-white rounded-lg transition-all duration-150 animate-fadeIn hover:border-primary">
      <div className="flex items-center w-full">
        <div className="mr-4 flex h-8 w-8 items-center justify-center rounded-2xl bg-[#718EBF1A]/10">
          <Icons.ChipExtractionRound className="h-[18px] w-[18px]" />
        </div>
        <div className="flex-1">
          <span className="block mb-1 text-xs font-normal text-subText sm:text-sm">
            {insight}
          </span>
          <span className="hidden text-xs text-gray-500">{timestamp}</span>
        </div>
      </div>
    </div>
  );
};

const AllInsightsPage: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const { filters } = useAnalyticsParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { tenantId } = useTenant();
  const pageSize = 10;

  // Get filters from URL params
  const searchParams = new URLSearchParams(location.search);
  const fromDate = searchParams.get('from') || undefined;
  const toDate = searchParams.get('to') || undefined;

  // Build filter for insights - include agent filter by default
  const insightFilter = {
    tenantId: tenantId || '',
    agentName: filters.agent || undefined,
    search: searchQuery,
    from: fromDate,
    to: toDate,
    page: currentPage - 1, // Convert to 0-based indexing
    pageSize: pageSize,
  };

  // Fetch insights using the hook
  const {
    data: insightsResponse,
    isLoading,
    isError,
  } = useDailyInsightsList(insightFilter, !!tenantId);

  const insights = insightsResponse?.insights || [];
  const totalCount = insightsResponse?.total || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // The API already handles search filtering, so we don't need to filter again here
  const filteredInsights = insights;

  return (
    <div className="flex flex-col gap-6 p-6 h-full">
      {/* Header */}
      <div className="flex relative gap-4 justify-between items-center">
        <div className="flex gap-2 items-start">
          <button onClick={() => navigate(-1)} className="mt-1">
            <ChevronLeft className="w-5 h-5" strokeWidth={3} />
          </button>
          <div className="flex flex-col gap-1 items-start text-blackTwo">
            <h1 className="text-lg font-semibold">
              Daily Insight Commentary (from {filters.agent})
            </h1>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex gap-3 items-center">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-[#979797]" />
            <input
              type="text"
              placeholder="Search insights..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
            />
          </div>
          <Button className="flex h-[44px] items-center gap-2 rounded-[10px] border-2 border-primary bg-[#FDF7F6] p-4 text-primary transition-colors hover:border-0 hover:bg-primary hover:text-white">
            <span>Filter</span>
            <Filter className="w-5 h-5" />
          </Button>
        </div>
      </div>

      {/* Insights Content */}
      <div className="flex flex-col flex-1 gap-4">
        {isLoading ? (
          <div className="py-6 space-y-4">
            {[...Array(5)].map((_, index) => (
              <div
                key={index}
                className="flex items-center space-x-4 animate-pulse"
              >
                <div className="w-12 h-12 bg-gray-200 rounded-2xl"></div>
                <div className="w-3/4 h-5 bg-gray-200 rounded-full"></div>
              </div>
            ))}
          </div>
        ) : isError ? (
          <div className="flex flex-col justify-center items-center py-12">
            <p className="mb-2 text-lg text-red-600">Error loading insights</p>
            <p className="text-sm text-gray-500">Please try again later</p>
          </div>
        ) : filteredInsights.length === 0 ? (
          <div className="flex flex-col justify-center items-center py-12">
            <p className="mb-2 text-lg text-gray-600">No insights found</p>
            <p className="text-sm text-gray-500">
              Try adjusting your search criteria or filters
            </p>
          </div>
        ) : (
          filteredInsights.map((insight) => (
            <InsightItem
              key={insight.id}
              id={insight.id}
              insight={insight.insight}
              type={'info'}
              timestamp={new Date(insight.createdAt).toLocaleString()}
            />
          ))
        )}
      </div>

      {/* Pagination */}
      {!isLoading && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onDownload={undefined}
          className="mt-auto"
          downloadButtonText="Download Report"
        />
      )}
    </div>
  );
};

export default AllInsightsPage;