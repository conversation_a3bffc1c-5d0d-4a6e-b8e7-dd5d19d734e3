import React from "react";
import { AgentSuite } from "../../../types/user";
import { agentSelector } from "../../../assets/images";
import { agentSuites as mockAgentsSuite } from "../../../data/constants";
import { ChevronRight } from "lucide-react";

export type UploadLevel = "suite" | "agent";

interface LevelOption {
  id: UploadLevel;
  title: string;
  description: string;
  image: string;
  showImage: boolean;
}

interface LevelSelectorProps {
  currentSuite?: AgentSuite;
  selectedLevel?: UploadLevel;
  onLevelSelect: (level: UploadLevel) => void;
  onNext: () => void;
}

const LevelSelector: React.FC<LevelSelectorProps> = ({
  currentSuite,
  selectedLevel,
  onLevelSelect,
  onNext,
}) => {
  const levelOptions: LevelOption[] = [
    {
      id: "suite" as const,
      title: "Suite Level",
      description: "Shared by all agents in this suite",
      image: currentSuite?.avatar || "",
      showImage: true,
    },
    {
      id: "agent" as const,
      title: "Agent Level",
      description: "Only used by the selected agent",
      image: agentSelector,
      showImage: true,
    },
  ];

  const handleCardClick = (level: UploadLevel) => {
    onLevelSelect(level);
    onNext();
  };

  return (
    <div className="flex flex-col items-start">
      {/* Header */}
      <h2 className="flex items-center gap-2 text-lg mb-6 font-medium capitalize text-subText">
        Select Level <ChevronRight /> Upload Knowledge Base
      </h2>

      {/* Selection Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {levelOptions.map((option) => (
          <div
            key={option.id}
            className="max-w-[290px] overflow-hidden rounded-2xl border border-gray-200 bg-white transition-shadow hover:border-primary cursor-pointer"
            onClick={() => handleCardClick(option.id)}
          >
            {/* Card Image */}
            <img
              src={option.image}
              className="object-cover w-full h-56"
              alt={option.title}
              onError={(e) => {
                // Fallback to mock logo if agent avatar fails to load
                (e.target as HTMLImageElement).src = mockAgentsSuite.filter(
                  (agent) => agent.id.toLowerCase() === option.id.toLowerCase()
                )[0].image;
              }}
            />

            {/* Card Content */}
            <div className="flex flex-col gap-2 p-6 text-blackOne">
              <div className="text-lg font-semibold">{option.title}</div>
              <p className="text-sm">{option.description}</p>
              <button
                className="rounded bg-primary px-4 py-2 mt-4 text-white transition-colors hover:bg-orange-15"
                onClick={() => handleCardClick(option.id)}
              >
                Select Level
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LevelSelector;
