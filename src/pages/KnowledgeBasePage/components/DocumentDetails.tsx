import React, { useState, useEffect } from "react";
import { Trash2, Upload, ChevronLeft, Download } from "lucide-react";
import AnimatedModal from "../../../components/common/AnimatedModal";
import KnowledgeBaseFileUploadModal from "./KnowledgeBaseFileUploadModal";
import { alertIcon, fileApprove } from "@/assets/icons";
import moment from "moment";
import FilePreview from "@/components/ui/FilePreview";
import { useFileActivityLogApi } from "@/services/upivotalAgenticService";
import { kbIcons } from "@/data/constants";
import { kbCurlLines } from "@/assets/images";

interface KnowledgeBaseDocument {
  iconUrl: string;
  name: string;
  key: string;
  fileRef?: string;
  description: string;
  hasFile?: boolean;
  fileName?: string;
  uploadedAt?: string;
  url?: string;
  size?: string;
  createdAt?: string;
}

interface DocumentDetailsProps {
  document: KnowledgeBaseDocument;
  onBack: () => void;
  onDelete?: (documentKey: string) => void;
  onUpload?: (documentKey: string, files: File[]) => void;
  onReplace?: (documentKey: string, files: File[]) => void;
  isLoading?: boolean;
  index?: number;
}

interface Activity {
  action: string;
  dateModified: string;
  modifiedBy: string;
  modifiedByFirstName: string;
  modifiedByLastName: string;
}

const DocumentDetails: React.FC<DocumentDetailsProps> = ({
  document: doc,
  onBack,
  onDelete,
  onUpload,
  onReplace,
  index = 0,
  isLoading = false,
}) => {
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [hasFile, setHasFile] = useState(doc.hasFile || false);
  const [activityLog, setActivityLog] = useState<Activity[]>([]);
  const [showAllActivities, setShowAllActivities] = useState(false);
  const [isUploadLoading, setIsUploadLoading] = useState(false);
  const [isActivityLogLoading, setIsActivityLogLoading] = useState(false);

  const activityLogApi = useFileActivityLogApi();

  const fetchActivityLog = async () => {
    if (!doc.fileRef) return;

    setIsActivityLogLoading(true);
    try {
      const log = await activityLogApi(doc.fileRef);
      // Sort by dateModified in descending order (most recent first)
      const sortedActivities = log.data.modifications.sort(
        (a: Activity, b: Activity) =>
          new Date(b.dateModified).getTime() -
          new Date(a.dateModified).getTime()
      );
      setActivityLog(sortedActivities);
    } catch (error) {
      console.error("Error fetching activity log:", error);
    } finally {
      setIsActivityLogLoading(false);
    }
  };

  useEffect(() => {
    fetchActivityLog();
  }, [doc.fileRef]);

  useEffect(() => {
    setHasFile(doc.hasFile || false);
  }, [doc.hasFile]);

  const handleDelete = () => {
    if (onDelete) {
      onDelete(doc.key);
      setDeleteModalOpen(false);
      // After successful delete, update hasFile state and refresh activity log
      setHasFile(false);
      setTimeout(() => {
        fetchActivityLog();
      }, 500);
    }
  };

  const handleUpload = async (files: File[]) => {
    setIsUploadLoading(true);
    try {
      if (hasFile && doc.fileRef && onReplace) {
        onReplace(doc.fileRef, files);
      } else if (onUpload) {
        onUpload(doc.key, files);
      }
      setUploadModalOpen(false);
      // After successful upload, update hasFile state and refresh activity log
      setHasFile(true);
      setTimeout(() => {
        fetchActivityLog();
      }, 500);
    } catch (error) {
      console.error("Upload failed:", error);
    } finally {
      setIsUploadLoading(false);
    }
  };

  const formatFileSize = (sizeInBytes: string): string => {
    const bytes = parseInt(sizeInBytes, 10);
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleDownload = () => {
    if (!doc.url) {
      console.error("No document URL available");
      return;
    }

    try {
      const link = document.createElement("a");
      link.href = doc.url;

      const downloadName =
        doc.fileName || doc.name || getFileNameFromUrl(doc.url);
      link.download = downloadName;
      link.target = "_blank"; // fallback
      link.style.display = "none";

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Download failed:", error);
      // Fallback to opening in new tab
      window.open(doc.url, "_blank");
    }
  };

  const getFileNameFromUrl = (url: string): string => {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      return pathname.substring(pathname.lastIndexOf("/") + 1);
    } catch {
      return "Knowledgebase document.pdf";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Back Button */}
      <div
        className="flex items-center justify-between bg-blue-midnight rounded p-6"
        style={{
          backgroundImage: `url(${kbCurlLines})`,
          backgroundSize: "contain",
          backgroundPosition: "right",
          backgroundRepeat: "no-repeat",
        }}
      >
        <div className="flex items-center gap-3">
          <button
            onClick={onBack}
            className="flex items-center justify-center w-5 h-5 rounded-lg transition-colors"
          >
            <ChevronLeft className="h-5 w-5 text-white hover:text-primary" />
          </button>
          <img
            className="h-8 w-8"
            src={doc.iconUrl}
            alt={doc.name}
            onError={(e) => {
              (e.target as HTMLImageElement).src =
                kbIcons[index % kbIcons.length];
            }}
          />
          <h1 className="font-semibold text-white font-spartan mt-0.5">
            {doc.name}
          </h1>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          {/* <button
            onClick={() => setDeleteModalOpen(true)}
            disabled={!hasFile || isLoading}
            className={`
            flex items-center gap-2 px-4 py-2 rounded-full border border-white transition-colors
            ${
              hasFile && !isLoading
                ? "hover:bg-red-50 hover:text-primary border-white hover:border-primary"
                : "border-grayThirteen text-grayThirteen cursor-not-allowed"
            }
          `}
          >
            <Trash2 className="h-4 w-4" />
          </button>

           <button
            onClick={handleEdit}
            disabled={!hasFile || isLoading}
            className={`
            flex items-center gap-2 px-4 py-2 rounded-full border border-grayTen transition-colors
            ${
              hasFile && !isLoading
                ? "hover:bg-red-50 hover:text-primary border-grayTen hover:border-primary"
                : "border-grayThirteen text-grayThirteen cursor-not-allowed"
            }
          `}
          >
            <PencilLine className="h-4 w-4" />
          </button> */}

          <button
            onClick={() => setUploadModalOpen(true)}
            disabled={isLoading || isUploadLoading}
            className={`
            flex items-center gap-2 px-4 py-2 rounded-full border border-white transition-colors text-sm font-medium
            ${
              isLoading || isUploadLoading
                ? "bg-grayTwo text-grayThirteen cursor-not-allowed"
                : "hover:bg-primary text-white hover:text-white hover:border-primary"
            }
          `}
          >
            {isLoading || isUploadLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Uploading...
              </>
            ) : (
              <>
                {hasFile ? "Replace" : "Upload"}
                <Upload className="h-4 w-4" />
              </>
            )}
          </button>
        </div>
      </div>

      {/* File Status Section */}
      {hasFile && doc.fileName && (
        <div className="flex items-center gap-4 h-[104px]">
          <div className="border rounded p-4 w-1/2 min-h-full">
            <div className="text-grayTen mb-2">Status</div>
            <div
              className={`flex items-center w-fit gap-2 px-5 py-2.5 rounded-full bg-grayTen border border-grayTen text-white text-xs font-medium`}
            >
              <img src={fileApprove} alt="file" />
              Awaiting Approval
            </div>
          </div>

          <div className="border rounded p-4 w-1/2 min-h-full">
            <div className="text-grayTen mb-2">Last Updated</div>
            {(() => {
              // Use the most recent dateModified from activity log, fallback to doc.createdAt
              const lastUpdatedDate =
                activityLog.length > 0
                  ? activityLog[0].dateModified // Activity log is already sorted by most recent first
                  : doc.createdAt;

              return (
                lastUpdatedDate && (
                  <div
                    className={`flex items-center w-fit gap-2 px-5 py-2.5 rounded-full border border-grayTen text-grayTen text-xs font-medium`}
                  >
                    {moment(lastUpdatedDate).format("LL")}
                  </div>
                )
              );
            })()}
          </div>
        </div>
      )}

      {/* Overview Section */}
      <div className="border rounded p-4">
        <div className="text-lg font-bold">Overview</div>
        <p className="font-medium mt-2.5">{doc.description}</p>
      </div>

      {/* Document Section */}
      {hasFile && doc.fileName && (
        <div className="border rounded p-4">
          <div className="text-lg font-bold mb-3">Document</div>
          <div className="flex items-center gap-3">
            <div
              className="relative group cursor-pointer"
              onClick={handleDownload}
              title={doc.url ? "Download file" : "File not available"}
            >
              {/* File Preview */}
              <FilePreview
                url={doc.url}
                fileName={doc.fileName}
                className="h-20 w-16 border rounded bg-white shadow-sm"
              />

              {doc.url && (
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/20 rounded">
                  <Download className="h-6 w-6 text-white" />
                </div>
              )}
            </div>

            <div className="text-grayTen">
              <p className="font-medium">{doc.fileName}</p>
              {doc.size && (
                <p className="text-sm">{formatFileSize(doc.size)}</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Activity Log Section */}
      {hasFile && (
        <div className="border rounded p-4">
          <div className="text-lg font-bold mb-3">Activity Log</div>
          {isActivityLogLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3 text-sm text-gray-600">
                Loading activity log...
              </span>
            </div>
          ) : activityLog.length > 0 ? (
            <div className="space-y-3">
              {(showAllActivities ? activityLog : activityLog.slice(0, 3)).map(
                (activity, index) => (
                  <div key={index} className="flex gap-4 items-center">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect
                        x="5.5"
                        y="5.5"
                        width="21"
                        height="21"
                        rx="10.5"
                        fill="white"
                      />
                      <rect
                        x="5.5"
                        y="5.5"
                        width="21"
                        height="21"
                        rx="10.5"
                        stroke="#FF3E00"
                        strokeWidth="11"
                      />
                    </svg>

                    <div>
                      <div className="text-sm text-gray-400 mb-1.5">
                        {moment(activity.dateModified).format("LLL")}
                      </div>
                      <div className="text-sm">{`${activity.modifiedByFirstName ?? ""} ${activity.modifiedByLastName ?? ""} ${activity.action.toLocaleLowerCase()} the document`}</div>
                    </div>
                  </div>
                )
              )}

              {/* Load More Button */}
              {activityLog.length > 3 && !showAllActivities && (
                <div className="flex justify-center pt-3">
                  <button
                    onClick={() => setShowAllActivities(true)}
                    className="px-4 py-2 text-sm font-medium text-primary border border-primary rounded-md hover:bg-primary hover:text-white transition-colors"
                  >
                    Load more
                  </button>
                </div>
              )}
            </div>
          ) : (
            <p className="text-sm text-gray-400">No activity log available</p>
          )}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <AnimatedModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Delete Document"
        maxWidth="md"
      >
        <div className="space-y-6 flex flex-col items-center justify-center">
          <img src={alertIcon} alt="" className="w-16 h-16" />
          <div className="text-subText font-medium text-center">
            You're going to delete the{" "}
            <span className="font-bold">"{doc.name}"</span> document.
          </div>

          <div className="flex gap-6 pt-4">
            <button
              onClick={() => setDeleteModalOpen(false)}
              className="px-6 py-3 bg-grayThirteen text-white text-sm font-medium rounded-lg hover:bg-grayTwentyOne transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleDelete}
              className="px-6 py-3 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Delete
            </button>
          </div>
        </div>
      </AnimatedModal>

      {/* Upload Modal */}
      <KnowledgeBaseFileUploadModal
        multiple={false}
        isOpen={uploadModalOpen}
        onClose={() => setUploadModalOpen(false)}
        title={hasFile ? `Replace ${doc.name}` : `Upload ${doc.name}`}
        onFilesUpload={handleUpload}
      />
    </div>
  );
};

export default DocumentDetails;
